# Docker构建指南

## 标准构建方法

如果您能正常访问Docker Hub，请使用以下命令构建镜像：

```bash
docker build -t tianluo_api .
```

## 运行容器

构建成功后，使用以下命令运行容器：

```
docker run -p 8000:8000 -e SUPABASE_URL=<your_supabase_url> -e SUPABASE_KEY=<your_supabase_key> -e DEBUG=True tianluo-api
```

```bash
docker run -p 8000:8000 -e SUPABASE_URL=https://efhtgyfhwaxqequunbhc.supabase.co -e SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVmaHRneWZod2F4cWVxdXVuYmhjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzk0MzMxMTMsImV4cCI6MjA1NTAwOTExM30.5jsufdamloTbAO19QPEk7MhqdTdUSFooS13hdZSruyA -e DEBUG=True ghcr.io/riftcover/tianluo_api:master

```

## 使用Docker Compose
```bash
docker-compose up -d
```