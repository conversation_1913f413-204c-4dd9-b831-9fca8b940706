version: '3.8'

networks:
  tianluo-network:
    driver: bridge

volumes:
  api-logs:
    driver: local

services:
  # 后端 API 服务
  api:
    image: ghcr.io/riftcover/tianluo_api:master
    container_name: tianluo_api
    ports:
      - "8000:8000"
    volumes:
      - api-logs:/app/logs
    environment:
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - DEBUG=${DEBUG:-False}
      - CORS_ORIGINS=${CORS_ORIGINS:-http://localhost:3000,http://localhost:80}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/health', timeout=10)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 15s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    networks:
      - tianluo-network

  # 前端 Web 服务
  web:
    image: ghcr.io/riftcover/tianluo:latest
    container_name: tianluo_web
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL:-http://localhost:8000}
      - HOSTNAME=0.0.0.0
      - PORT=3000
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "-e", "const http = require('http'); const req = http.request({hostname: 'localhost', port: 3000, path: '/', timeout: 10000}, (res) => { process.exit(res.statusCode === 200 ? 0 : 1); }); req.on('error', () => process.exit(1)); req.end();"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    networks:
      - tianluo-network
    depends_on:
      api:
        condition: service_healthy


# 可选：Redis 缓存服务
  # redis:
  #   image: redis:7-alpine
  #   container_name: tianluo_redis
  #   ports:
  #     - "6379:6379"
  #   volumes:
  #     - redis-data:/data
  #   restart: unless-stopped
  #   healthcheck:
  #     test: ["CMD", "redis-cli", "ping"]
  #     interval: 10s
  #     timeout: 5s
  #     retries: 3
  #   networks:
  #     - tianluo-network

# 可选：数据库服务（如果不使用外部数据库）
  # postgres:
  #   image: postgres:15-alpine
  #   container_name: tianluo_postgres
  #   environment:
  #     - POSTGRES_DB=${POSTGRES_DB:-tianluo}
  #     - POSTGRES_USER=${POSTGRES_USER:-tianluo}
  #     - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
  #   volumes:
  #     - postgres-data:/var/lib/postgresql/data
  #   restart: unless-stopped
  #   healthcheck:
  #     test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-tianluo}"]
  #     interval: 10s
  #     timeout: 5s
  #     retries: 3
  #   networks:
  #     - tianluo-network 